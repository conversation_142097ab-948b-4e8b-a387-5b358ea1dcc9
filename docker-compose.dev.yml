version: "3.8"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: gw-crm-api:dev
    ports:
      - "9091:9091"
    secrets:
      - gw_crm_dev_database_url
      - gw_crm_dev_database_auth_token
      - gw_crm_dev_better_auth_secret
      - gw_crm_dev_resend_api_key
      - gw_crm_dev_telegram_bot_token
      - gw_crm_dev_redis_password
    environment:
      - NODE_ENV=development
      - PORT=9091
      - LOG_LEVEL=info
      - BASE_URL=https://dev-api.crm-group-working.com
      - HOST=0.0.0.0
      - EMAIL_FROM=<EMAIL>
      - TELEGRAM_CHANNEL_ID=@jobvacancytestergw
      - RESEND_API_KEY_FILE=/run/secrets/gw_crm_dev_resend_api_key
      - DATABASE_URL_FILE=/run/secrets/gw_crm_dev_database_url
      - DATABASE_AUTH_TOKEN_FILE=/run/secrets/gw_crm_dev_database_auth_token
      - BETTER_AUTH_SECRET_FILE=/run/secrets/gw_crm_dev_better_auth_secret
      - TELEGRAM_BOT_TOKEN_FILE=/run/secrets/gw_crm_dev_telegram_bot_token
      - REDIS_PASSWORD_FILE=/run/secrets/gw_crm_dev_redis_password
      - REDIS_HOST=**************
      - REDIS_PORT=6379
    healthcheck:
      test: [CMD, wget, --spider, -q, "http://0.0.0.0:9091/doc"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      replicas: 1
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        max_attempts: 3
        window: 120s
    networks:
      - api-network

networks:
  api-network:
    driver: overlay

secrets:
  gw_crm_dev_database_url:
    external: true
  gw_crm_dev_database_auth_token:
    external: true
  gw_crm_dev_better_auth_secret:
    external: true
  gw_crm_dev_resend_api_key:
    external: true
  gw_crm_dev_telegram_bot_token:
    external: true
  gw_crm_dev_redis_password:
    external: true
