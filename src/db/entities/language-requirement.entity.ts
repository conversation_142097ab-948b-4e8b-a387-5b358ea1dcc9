import { REQUIREMENTS_LANGUAGES } from "@/lib/constants";
import { primaryKey, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { type JobVacancy, jobVacancy } from "./job-vacancy.entity";

export const languageRequirement = sqliteTable(
  "language_requirement",
  {
    jobVacancyId: text("job_vacancy_id")
      .notNull()
      .references(() => jobVacancy.id, { onDelete: "cascade" }),
    language: text("language", { enum: REQUIREMENTS_LANGUAGES }).notNull(),
    level: text("level", { enum: ["none", "low", "mid", "high"] }).notNull(),
  },
  (table) => [primaryKey({ columns: [table.jobVacancyId, table.language] })],
);

export const insertLanguageRequirementSchema =
  createInsertSchema(languageRequirement);

export const selectLanguageRequirementSchema = createSelectSchema(
  languageRequirement,
).omit({ jobVacancyId: true });

export type LanguageRequirement = typeof languageRequirement.$inferSelect;
export type NewLanguageRequirement = typeof languageRequirement.$inferInsert;

export type JobWithLanguageReqs = JobVacancy & {
  languageRequirements: Omit<LanguageRequirement, "jobVacancyId">[];
};
