import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const authorDetails = sqliteTable("author_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  bio: text("bio"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertAuthorDetailsSchema = createInsertSchema(authorDetails);
export const selectAuthorDetailsSchema = createSelectSchema(authorDetails);
export const patchAuthorDetailsSchema =
  createSelectSchema(authorDetails).partial();

export type AuthorDetails = typeof authorDetails.$inferSelect;
export type NewAuthorDetails = typeof authorDetails.$inferInsert;
