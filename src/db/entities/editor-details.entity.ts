import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const editorDetails = sqliteTable("editor_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertEditorDetailsSchema = createInsertSchema(editorDetails);
export const selectEditorDetailsSchema = createSelectSchema(editorDetails);
export const patchEditorDetailsSchema =
  createSelectSchema(editorDetails).partial();

export type EditorDetails = typeof editorDetails.$inferSelect;
export type NewEditorDetails = typeof editorDetails.$inferInsert;
