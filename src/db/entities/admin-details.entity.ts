import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const adminDetails = sqliteTable("admin_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertAdminDetailsSchema = createInsertSchema(adminDetails);
export const selectAdminDetailsSchema = createSelectSchema(adminDetails);
export const patchAdminDetailsSchema =
  createSelectSchema(adminDetails).partial();

export type AdminDetails = typeof adminDetails.$inferSelect;
export type NewAdminDetails = typeof adminDetails.$inferInsert;
