import { SUPPORTED_LANGUAGES } from "@/lib/constants";
import { generateId } from "@/lib/helpers";
import { integer, sqliteTable, text, unique } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const translationKey = sqliteTable("translation_key", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  defaultText: text("default_text").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const translation = sqliteTable(
  "translation",
  {
    textId: text("text_id")
      .notNull()
      .references(() => translationKey.id),
    language: text("language", { enum: SUPPORTED_LANGUAGES }).notNull(),
    content: text("content").notNull(),
    createdAt: integer("created_at", { mode: "timestamp" })
      .notNull()
      .$defaultFn(() => new Date()),
    updatedAt: integer("updated_at", { mode: "timestamp" })
      .notNull()
      .$defaultFn(() => new Date())
      .$onUpdate(() => new Date()),
  },
  (table) => [unique().on(table.textId, table.language)],
);

export const insertTranslationKeySchema = createInsertSchema(translationKey);
export const selectTranslationKeySchema = createSelectSchema(translationKey);

export const insertTranslationSchema = createInsertSchema(translation);
export const selectTranslationSchema = createSelectSchema(translation);

export type TranslationKey = typeof translationKey.$inferSelect;
export type NewTranslationKey = typeof translationKey.$inferInsert;

export type Translation = typeof translation.$inferSelect;
export type NewTranslation = typeof translation.$inferInsert;
