import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { generateId } from "@/lib/helpers";

import { user } from "../user.entity";

export const session = sqliteTable("session", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  expiresAt: integer("expires_at", { mode: "timestamp" }).notNull(),
  token: text("token").notNull().unique(),
  createdAt: integer("created_at", { mode: "timestamp" }).notNull(),
  updatedAt: integer("updated_at", { mode: "timestamp" }).notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  impersonatedBy: text("impersonated_by").references(() => user.id),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

export const insertSessionSchema = createInsertSchema(session);
export const selectSessionSchema = createSelectSchema(session);
export const patchSessionSchema = createSelectSchema(session).partial();

export type Session = typeof session.$inferSelect;
export type NewSession = typeof session.$inferInsert;
