import { GENDERS, WORKPLACE_CODES } from "@/lib/constants";
import { generateId } from "@/lib/helpers";
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { translationKey } from "./translation.entity";
import { user } from "./user.entity";

export const jobVacancy = sqliteTable("job_vacancy", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  sequence: integer("sequence").notNull().unique(),
  slug: text("slug").notNull().unique(),
  titleTextId: text("title_text_id")
    .references(() => translationKey.id)
    .notNull(),
  country: text("country").notNull(),
  cities: text("cities", { mode: "json" }).$type<string[]>().notNull(),
  workwearTextId: text("workwear_text_id").references(() => translationKey.id),
  responsibilitiesTextId: text("responsibilities_text_id")
    .references(() => translationKey.id)
    .notNull(),
  experience: text("experience", {
    enum: ["not_required", "minimal", "required", "not_required_but_preferred"],
  }).notNull(),
  salary: text("salary").notNull(),
  descriptionTextId: text("description_text_id")
    .references(() => translationKey.id)
    .notNull(),
  genders: text("genders", { mode: "json" })
    .$type<(typeof GENDERS)[number][]>()
    .notNull(),
  workScheduleTextId: text("work_schedule_text_id")
    .references(() => translationKey.id)
    .notNull(),
  statistics: text("statistics"),
  status: text("status", { enum: ["draft", "published", "archived"] })
    .notNull()
    .default("draft"),
  notes: text("notes"),
  jobType: text("job_type").notNull(),
  workplace: text("workplace", { enum: WORKPLACE_CODES }),
  authorId: text("author_id")
    .notNull()
    .references(() => user.id),
  editorId: text("editor_id").references(() => user.id),
  archivedAt: integer("archived_at", { mode: "timestamp" }),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
  numberOfPositions: integer("number_of_positions").notNull().default(1),
  housing: text("housing", {
    enum: ["provided", "not_provided", "partially_compensated"],
  }),
});

export const insertJobVacancySchema = createInsertSchema(jobVacancy, {
  cities: z.array(z.string().min(1)),
  genders: z.array(z.enum(GENDERS)).min(1),
  housing: z
    .enum(["provided", "not_provided", "partially_compensated"])
    .optional(),
  workplace: z.enum(WORKPLACE_CODES).nullable().optional(),
});

export const selectJobVacancySchema = createSelectSchema(jobVacancy, {
  cities: z.array(z.string()),
  genders: z.array(z.enum(GENDERS)),
  housing: z
    .enum(["provided", "not_provided", "partially_compensated"])
    .optional()
    .nullable(),
  workplace: z.enum(WORKPLACE_CODES).nullable().optional(),
});

export const patchJobVacancySchema = selectJobVacancySchema.partial().omit({
  id: true,
  authorId: true,
  archivedAt: true,
  createdAt: true,
  updatedAt: true,
});

export type JobVacancy = typeof jobVacancy.$inferSelect;
export type NewJobVacancy = typeof jobVacancy.$inferInsert;
