import { appClient } from "@/app";
import env from "@/env";
import {
  cleanupTestDb,
  setupTestDb,
  setupTestUser,
} from "@/lib/utils/test.utils";
import {
  afterAll,
  beforeAll,
  describe,
  expect,
  expectTypeOf,
  it,
} from "vitest";

if (env.NODE_ENV !== "test") {
  throw new Error("NODE_ENV must be 'test'");
}

describe("job vacancies routes", () => {
  const testData = {
    id: "test-job-id",
    country: "Germany",
    cities: ["Berlin", "Munich"],
    specialty: "Software Engineering",
    experience: "not_required" as const,
    salary: "70000-90000",
    genders: ["male" as const, "female" as const],
    jobType: "Permanent",
    status: "draft" as const,
    specialties: ["others" as const],
    title: {
      en: "Senior Software Engineer",
      de: "Senior Softwareentwickler",
    },
    workwear: {
      en: "Business casual",
      de: "Business-Casual",
    },
    responsibilities: {
      en: "Leading development team",
      de: "Entwicklungsteam leiten",
    },
    description: {
      en: "Looking for experienced developer",
      de: "Suchen erfahrenen Entwickler",
    },
    workSchedule: {
      en: "Full-time",
      de: "Vollzeit",
    },
    languageRequirements: [],
    numberOfPositions: 2,
    housing: "provided" as const,
    workplace: "factory" as const,
  };

  const updateData = {
    status: "published" as const,
    salary: "75000-95000",
    description: {
      en: "Updated job description",
      de: "Aktualisierte Stellenbeschreibung",
    },
    workplace: "warehouse" as const,
  };

  let addAuthHeaders: () => Record<string, string>;

  beforeAll(async () => {
    setupTestDb();
    const auth = await setupTestUser("author");
    addAuthHeaders = auth.addAuthHeaders;
  });

  afterAll(() => {
    cleanupTestDb();
  });

  describe("GET /jobs", () => {
    it("lists all vacancies without auth", async () => {
      const response = await appClient.jobs.$get();
      expect(response.status).toBe(200);
      const json = await response.json();
      expectTypeOf(json).toMatchTypeOf<{
        items: unknown[];
        total: number;
        page?: number;
        limit?: number;
        pages?: number;
      }>();
    });

    // it("supports pagination", async () => {
    //   // Create multiple vacancies first
    //   await Promise.all([
    //     appClient.jobs.$post(
    //       { json: { ...testData, id: "test-job-1" } },
    //       { headers: addAuthHeaders() },
    //     ),
    //     appClient.jobs.$post(
    //       { json: { ...testData, id: "test-job-2" } },
    //       { headers: addAuthHeaders() },
    //     ),
    //   ]);

    //   const response = await appClient.jobs.$get({
    //     query: {
    //       page: "1",
    //       limit: "1",
    //       sortBy: "createdAt",
    //       sortDirection: "desc",
    //     },
    //   });

    //   expect(response.status).toBe(200);
    //   const json = await response.json();
    //   expect(json).toMatchObject({
    //     items: expect.any(Array),
    //     total: expect.any(Number),
    //     page: 1,
    //     limit: 1,
    //     pages: expect.any(Number),
    //   });
    //   expect(json.items).toHaveLength(1);
    // });
  });

  it("POST /jobs creates a vacancy with translations", async () => {
    const response = await appClient.jobs.$post(
      {
        json: testData,
      },
      {
        headers: addAuthHeaders(),
      },
    );

    expect(response.status).toBe(201);
    const json = await response.json();
    expect(json).toMatchObject({
      status: "draft",
      title: expect.objectContaining({
        en: testData.title.en,
        de: testData.title.de,
      }),
      description: expect.objectContaining({
        en: testData.description.en,
        de: testData.description.de,
      }),
      workplace: testData.workplace,
    });
  });

  it("PATCH /jobs/:id updates a vacancy with translations", async () => {
    const response = await appClient.jobs[":id"].$patch(
      {
        param: { id: testData.id },
        json: updateData,
      },
      {
        headers: addAuthHeaders(),
      },
    );

    expect(response.status).toBe(200);
    const json = await response.json();
    expect(json).toMatchObject({
      status: updateData.status,
      salary: updateData.salary,
      description: expect.objectContaining({
        en: updateData.description.en,
        de: updateData.description.de,
      }),
      workplace: updateData.workplace,
    });
  });

  it("DELETE /jobs/:id deletes a vacancy", async () => {
    const response = await appClient.jobs[":id"].$delete(
      {
        param: { id: testData.id },
      },
      {
        headers: addAuthHeaders(),
      },
    );

    expect(response.status).toBe(204);
  });
});
