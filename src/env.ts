import fs from "node:fs";
import path from "node:path";
/* eslint-disable node/no-process-env */
import { config } from "dotenv";
import { expand } from "dotenv-expand";
import { z } from "zod";

function getEnvValue(key: string): string | undefined {
  const fileEnvKey = `${key}_FILE`;
  const secretPath = process.env[fileEnvKey];

  if (secretPath && fs.existsSync(secretPath)) {
    try {
      return fs.readFileSync(secretPath, "utf8").trim();
    } catch (error) {
      console.warn(`Failed to read secret from ${secretPath}`);
      console.warn(error);
    }
  }

  return process.env[key];
}

expand(
  config({
    path: path.resolve(
      process.cwd(),
      process.env.NODE_ENV === "test" ? ".env.test" : ".env",
    ),
  }),
);

const envWithSecrets = {
  ...process.env,
  DATABASE_URL: getEnvValue("DATABASE_URL"),
  DATABASE_AUTH_TOKEN: getEnvValue("DATABASE_AUTH_TOKEN"),
  BETTER_AUTH_SECRET: getEnvValue("BETTER_AUTH_SECRET"),
  RESEND_API_KEY: getEnvValue("RESEND_API_KEY"),
  TELEGRAM_BOT_TOKEN: getEnvValue("TELEGRAM_BOT_TOKEN"),
  REDIS_PASSWORD: getEnvValue("REDIS_PASSWORD"),
};

const EnvSchema = z
  .object({
    NODE_ENV: z.string().default("development"),
    PORT: z.coerce.number().default(9999),
    LOG_LEVEL: z.enum([
      "fatal",
      "error",
      "warn",
      "info",
      "debug",
      "trace",
      "silent",
    ]),
    DATABASE_URL: z.string().url(),
    DATABASE_AUTH_TOKEN: z.string().optional(),
    BASE_URL: z
      .string()
      .default("http://localhost:9999")
      .transform((val) => {
        let newVal = val;
        if (val.startsWith("/")) {
          newVal = `http://localhost:9999${val}`;
        }
        try {
          const url = new URL(newVal);
          return url.toString().replace(/\/$/, "");
        } catch (error) {
          console.error(error);
          throw new Error(
            `Invalid BASE_URL: ${newVal}. Please provide a valid URL.`,
          );
        }
      }),
    BETTER_AUTH_SECRET: z.string(),
    EMAIL_FROM: z.string().email(),
    RESEND_API_KEY: z.string(),
    TELEGRAM_BOT_TOKEN: z.string(),
    TELEGRAM_CHANNEL_ID: z.string(),
    REDIS_HOST: z.string().default("redis"),
    REDIS_PORT: z.coerce.number().default(6379),
    REDIS_PASSWORD: z.string().optional(),
  })
  .superRefine((input, ctx) => {
    if (input.NODE_ENV === "production" && !input.DATABASE_AUTH_TOKEN) {
      ctx.addIssue({
        code: z.ZodIssueCode.invalid_type,
        expected: "string",
        received: "undefined",
        path: ["DATABASE_AUTH_TOKEN"],
        message: "Must be set when NODE_ENV is 'production'",
      });
    }
  })
  .transform((val) => ({
    ...val,
    isDevelopment: val.NODE_ENV === "development",
    isProduction: val.NODE_ENV === "production",
    isTest: val.NODE_ENV === "test",
  }));

// const newEnvSchema = type({
//   NODE_ENV: "string = 'development'",
//   PORT: type("string").pipe()
// });

export type EnvType = z.infer<typeof EnvSchema>;

const { data: env, error } = EnvSchema.safeParse(envWithSecrets);

if (error) {
  console.error("❌ Invalid env:");
  console.error(JSON.stringify(error.flatten().fieldErrors, null, 2));
  process.exit(1);
}
export default env!;
