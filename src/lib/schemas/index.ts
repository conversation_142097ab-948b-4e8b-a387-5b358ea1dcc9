export * from "./shared";
import { selectUserSchema } from "@/db/schema";
import { z } from "zod";

export const UserDTO = selectUserSchema.pick({
  id: true,
  name: true,
  description: true,
  email: true,
  image: true,
  phoneNumber: true,
  dateOfBirth: true,
  gender: true,
  role: true,
  createdAt: true,
  updatedAt: true,
});

export const listUsersDTO = z.object({
  items: z.array(UserDTO),
  total: z.number().int().min(0),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).optional(),
  pages: z.number().int().min(0).optional(),
});

export const AuthorDTO = selectUserSchema
  .pick({
    id: true,
    name: true,
    description: true,
    email: true,
    image: true,
    phoneNumber: true,
    dateOfBirth: true,
    gender: true,
    role: true,
    createdAt: true,
    updatedAt: true,
  })
  .extend({
    bio: z.string().optional(),
  });

export const listAuthorsDTO = z.object({
  items: z.array(AuthorDTO),
  total: z.number().int().min(0),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).optional(),
  pages: z.number().int().min(0).optional(),
});

export const patchUserSchema = selectUserSchema
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    emailVerified: true,
    role: true,
    email: true,
    banExpires: true,
    banned: true,
    banReason: true,
  })
  .partial();

export const userFilterSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  email: z.string().optional(),
  role: z.enum(["admin", "author", "editor"]).optional(),
  gender: z.enum(["male", "female"]).optional(),
  banned: z.boolean().optional(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
});
